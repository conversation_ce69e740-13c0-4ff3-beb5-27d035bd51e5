// Member Page JavaScript
// Global variables
let selectedDepositAmount = 0;
let selectedPaymentMethod = '';
let selectedTransferAmount = 0;
let selectedWithdrawAmount = 0;
let selectedWithdrawMethod = '';

document.addEventListener('DOMContentLoaded', function() {
    // Load service prices
    function loadServicePrices() {
        console.log('Loading service prices...');

        // Load optimize prices
        fetch('service_prices_api.php?action=get_prices&category=optimize&active_only=true')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayPrices('optimizePrices', data.prices);
                } else {
                    document.getElementById('optimizePrices').innerHTML = '<div style="color: #ff6b6b;">Error loading optimize prices</div>';
                }
            })
            .catch(error => {
                console.error('Error loading optimize prices:', error);
                document.getElementById('optimizePrices').innerHTML = '<div style="color: #ff6b6b;">Error loading optimize prices</div>';
            });

        // Load print prices
        fetch('service_prices_api.php?action=get_prices&category=print&active_only=true')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayPrices('printPrices', data.prices);
                } else {
                    document.getElementById('printPrices').innerHTML = '<div style="color: #ff6b6b;">Error loading print prices</div>';
                }
            })
            .catch(error => {
                console.error('Error loading print prices:', error);
                document.getElementById('printPrices').innerHTML = '<div style="color: #ff6b6b;">Error loading print prices</div>';
            });
    }

    // Display prices in the UI
    function displayPrices(containerId, prices) {
        const container = document.getElementById(containerId);
        if (!prices || prices.length === 0) {
            container.innerHTML = '<div style="color: #ccc;">No services available</div>';
            return;
        }

        let html = '';
        prices.forEach(price => {
            const lang = document.documentElement.lang || 'en';
            let itemName = price.item_name;

            // Use localized name if available
            if (lang === 'zh-CN' && price.item_name_zh) {
                itemName = price.item_name_zh;
            } else if (lang === 'en' && price.item_name_en) {
                itemName = price.item_name_en;
            }

            html += `
                <div class="price-item">
                    <div class="price-name">${itemName}</div>
                    <div>
                        <span class="price-value">$${parseFloat(price.base_price).toFixed(2)}</span>
                        <span class="price-unit">${price.unit}</span>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    // Load affiliate information
    function loadAffiliateInfo() {
        console.log('Loading affiliate info...');
        
        // Create URL with relative path and timestamp to prevent caching
        const url = new URL('affiliate_api.php', window.location.href);
        url.searchParams.append('action', 'get_affiliate_info');
        url.searchParams.append('t', Date.now());
        
        console.log('Fetching from:', url.toString());
        
        console.log('Starting fetch request to:', url.toString());
        
        fetch(url, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(async response => {
            console.log('Received response:', response.status, response.statusText);
            
            // First, read the response as text
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            
            // Try to parse as JSON
            try {
                const data = responseText ? JSON.parse(responseText) : {};
                
                // If we got here, it's valid JSON
                if (!response.ok) {
                    throw new Error(data.message || `HTTP error! status: ${response.status}`);
                }
                return data;
            } catch (e) {
                // If we can't parse as JSON, show the raw response
                console.error('Failed to parse JSON:', e);
                throw new Error(`Server returned invalid JSON: ${responseText.substring(0, 200)}`);
            }
        })
        .then(data => {
            console.log('Affiliate data received:', data);
            if (data.success && data.affiliate) {
                // Update the affiliate code display
                document.getElementById('affiliateCode').textContent = data.affiliate.code;
                // Update other affiliate stats
                document.getElementById('totalReferrals').textContent = data.affiliate.total_referrals;
                document.getElementById('totalCommissions').textContent = '$' + data.affiliate.total_commissions;
                document.getElementById('commissionBalance').textContent = '$' + 
                    (parseFloat(data.affiliate.total_commissions) - parseFloat(data.affiliate.total_withdrawn || 0)).toFixed(2);
                
                // Store affiliate info for later use
                window.affiliateInfo = data.affiliate;
            } else {
                const errorMsg = data && data.message ? data.message : 'No data received';
                console.error('Failed to load affiliate info:', errorMsg);
                document.getElementById('affiliateCode').textContent = 'Error: ' + errorMsg;
                console.log('Full response data:', data);
            }
        })
        .catch(error => {
            console.error('Error in fetch operation:', error);
            document.getElementById('affiliateCode').textContent = 'Error: ' + error.message;
        });
    }

    // Load wallet information
    function loadWalletInfo() {
        fetch(`credit_wallet.php?action=get_wallet&PHPSESSID=${SESSION_ID}`, {
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('walletBalance').textContent = '$' + parseFloat(data.balance).toFixed(2);
                document.getElementById('totalDeposited').textContent = '$' + parseFloat(data.total_deposited || 0).toFixed(2);
                document.getElementById('totalSpent').textContent = '$' + parseFloat(data.total_spent || 0).toFixed(2);
                document.getElementById('frozenBalance').textContent = '$' + parseFloat(data.frozen_balance || 0).toFixed(2);
            }
        })
        .catch(error => console.error('Error loading wallet info:', error));
    }

    // Load orders information
    function loadOrdersInfo() {
        console.log('Loading orders...');
        
        fetch(`get_user_orders.php?PHPSESSID=${SESSION_ID}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(async response => {
            console.log('Orders response status:', response.status);
            
            const responseText = await response.text();
            console.log('Orders raw response:', responseText);
            
            try {
                const data = JSON.parse(responseText);
                console.log('Orders parsed data:', data);
                return data;
            } catch (e) {
                console.error('Failed to parse orders JSON:', e);
                throw new Error('Invalid JSON response: ' + responseText.substring(0, 200));
            }
        })
        .then(data => {
            console.log('Orders data:', data);
            const ordersList = document.getElementById('ordersList');
            
            if (data.success && data.orders && data.orders.length > 0) {
                ordersList.innerHTML = '';
                
                data.orders.forEach(order => {
                    const orderDiv = document.createElement('div');
                    orderDiv.style.cssText = 'background: rgba(0,0,0,0.1); padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #00ffff;';
                    
                    const statusColor = getStatusColor(order.status);
                    
                    orderDiv.innerHTML = `
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <div style="font-weight: bold; color: #00ffff;">${order.order_number || 'Order #' + order.id}</div>
                            <div style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${order.status_display || order.status}</div>
                        </div>
                        <div style="margin-bottom: 8px;">${order.order_details || 'No details available'}</div>
                        <div style="display: flex; justify-content: space-between; font-size: 14px; color: #ccc;">
                            <div>Price: $${order.final_price || order.price || '0.00'}</div>
                            <div>Created: ${new Date(order.created_at).toLocaleDateString()}</div>
                        </div>
                        <div style="font-size: 12px; color: #aaa; margin-top: 5px;">
                            Service: ${order.service_type || 'General'} | Payment: ${order.payment_method || 'N/A'}
                        </div>
                    `;
                    
                    ordersList.appendChild(orderDiv);
                });
            } else {
                const message = data.message || 'No orders found';
                let debugInfo = '';
                
                if (data.debug) {
                    debugInfo = `<div style="font-size: 12px; color: #888; margin-top: 10px;">Debug: ${JSON.stringify(data.debug)}</div>`;
                }
                
                ordersList.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #ccc;">
                        ${message}
                        ${debugInfo}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading orders:', error);
            document.getElementById('ordersList').innerHTML = `
                <div style="text-align: center; padding: 20px; color: #ff6b6b;">
                    Error loading orders: ${error.message}
                </div>
            `;
        });
    }
    
    // Helper function to get status color
    function getStatusColor(status) {
        const colors = {
            'pending': '#ffa500',
            'processing': '#2196F3',
            'completed': '#4CAF50',
            'cancelled': '#f44336',
            'shipped': '#9C27B0'
        };
        return colors[status] || '#666';
    }

    // Initialize modals and other UI elements
    function initUI() {
        // Account Settings button
        document.getElementById('accountSettingsBtn').addEventListener('click', () => {
            window.location.href = 'account_settings.php';
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            if (confirm('Are you sure you want to log out?')) {
                window.location.href = 'logout.php';
            }
        });

        // Load data
        loadWalletInfo();
        loadAffiliateInfo();
        loadOrdersInfo();
        loadServicePrices();
    }

    // Initialize the page
    initUI();

    // Initialize PC Builder
    initPCBuilder();
});

// Order modal functions
function openOrderModal(category) {
    // Load services for the category
    fetch(`service_prices_api.php?action=get_prices&category=${category}&active_only=true`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOrderModal(category, data.prices);
            } else {
                showCustomAlert('error', 'Error Loading Services', 'Error loading services: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showCustomAlert('error', 'Connection Error', 'Error loading services');
        });
}

function showOrderModal(category, services) {
    const categoryName = category === 'optimize' ? 'Optimize Photo & Video' : 'Print Services';
    const lang = document.documentElement.lang || 'en';

    let servicesHtml = '';
    services.forEach(service => {
        let itemName = service.item_name;
        if (lang === 'zh-CN' && service.item_name_zh) {
            itemName = service.item_name_zh;
        } else if (lang === 'en' && service.item_name_en) {
            itemName = service.item_name_en;
        }

        servicesHtml += `
            <div class="service-item" data-service-id="${service.id}" data-price="${service.base_price}">
                <div class="service-info">
                    <div class="service-name">${itemName}</div>
                    <div class="service-price">$${parseFloat(service.base_price).toFixed(2)} ${service.unit}</div>
                </div>
                <div class="quantity-control">
                    <button type="button" onclick="changeQuantity(${service.id}, -1)">-</button>
                    <input type="number" id="qty-${service.id}" value="0" min="0" max="100" onchange="updateQuantity(${service.id})">
                    <button type="button" onclick="changeQuantity(${service.id}, 1)">+</button>
                </div>
            </div>
        `;
    });

    const modalHtml = `
        <div class="order-modal-content">
            <h2>Order ${categoryName}</h2>
            <div class="services-list">
                ${servicesHtml}
            </div>
            <div class="order-summary">
                <div class="summary-line">
                    <span>Subtotal:</span>
                    <span id="order-subtotal">$0.00</span>
                </div>
                <div class="summary-line">
                    <span>Discount:</span>
                    <span id="order-discount">$0.00</span>
                </div>
                <div class="summary-line">
                    <span>Shipping:</span>
                    <span id="order-shipping">$0.00</span>
                </div>
                <div class="summary-line total">
                    <span>Total:</span>
                    <span id="order-total">$0.00</span>
                </div>
            </div>
            <div class="order-options">
                <label>
                    <input type="checkbox" id="use-credit" checked>
                    Pay with KMS Credit
                </label>
                <textarea id="order-notes" placeholder="Additional notes (optional)"></textarea>
            </div>
            <div class="order-actions">
                <button class="btn-success" onclick="submitOrder('${category}')">Place Order</button>
                <button class="btn-secondary" onclick="closeOrderModal()">Cancel</button>
            </div>
        </div>
    `;

    showModal('order-modal', 'Place Order', modalHtml);
}

function changeQuantity(serviceId, change) {
    const input = document.getElementById(`qty-${serviceId}`);
    const currentValue = parseInt(input.value) || 0;
    const newValue = Math.max(0, Math.min(100, currentValue + change));
    input.value = newValue;
    updateQuantity(serviceId);
}

function updateQuantity(serviceId) {
    calculateOrderTotal();
}

function calculateOrderTotal() {
    const serviceItems = document.querySelectorAll('.service-item');
    let subtotal = 0;

    serviceItems.forEach(item => {
        const serviceId = item.dataset.serviceId;
        const price = parseFloat(item.dataset.price);
        const quantity = parseInt(document.getElementById(`qty-${serviceId}`).value) || 0;
        subtotal += price * quantity;
    });

    // Calculate discount (10% for orders over $50)
    const discount = subtotal > 50 ? subtotal * 0.1 : 0;

    // Calculate shipping (free for orders over $100)
    const shipping = (subtotal < 100 && subtotal > 0) ? 5.00 : 0;

    const total = subtotal - discount + shipping;

    document.getElementById('order-subtotal').textContent = '$' + subtotal.toFixed(2);
    document.getElementById('order-discount').textContent = '-$' + discount.toFixed(2);
    document.getElementById('order-shipping').textContent = '$' + shipping.toFixed(2);
    document.getElementById('order-total').textContent = '$' + total.toFixed(2);
}

function submitOrder(category) {
    const serviceItems = document.querySelectorAll('.service-item');
    const orderItems = [];

    serviceItems.forEach(item => {
        const serviceId = item.dataset.serviceId;
        const quantity = parseInt(document.getElementById(`qty-${serviceId}`).value) || 0;

        if (quantity > 0) {
            orderItems.push({
                service_id: parseInt(serviceId),
                quantity: quantity
            });
        }
    });

    if (orderItems.length === 0) {
        showCustomAlert('warning', 'No Services Selected', 'Please select at least one service');
        return;
    }

    const useCredit = document.getElementById('use-credit').checked;
    const notes = document.getElementById('order-notes').value;

    const formData = new FormData();
    formData.append('action', 'create_order');
    formData.append('service_category', category);
    formData.append('order_items', JSON.stringify(orderItems));
    formData.append('use_credit', useCredit ? '1' : '0');
    formData.append('notes', notes);

    fetch('service_orders_api.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showCustomAlert('success', 'Order Created Successfully', `Order created successfully! Order #${data.order_number}`);
            closeOrderModal();
            loadWalletInfo(); // Refresh wallet balance
            loadOrdersInfo(); // Refresh orders list
        } else {
            showCustomAlert('error', 'Order Creation Failed', 'Error creating order: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showCustomAlert('error', 'Connection Error', 'Error creating order');
    });
}

function closeOrderModal() {
    const modal = document.getElementById('order-modal');
    if (modal) {
        modal.remove();
    }
}
