/* Member Page Styles */
body {
    font-family: Arial, sans-serif;
    background-color: #a48f19;
    color: white;
    margin: 0;
    padding: 20px;
}

/* Top buttons */
.top-buttons {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.account-settings-btn {
    padding: 10px 20px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.logout-btn {
    padding: 10px 20px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* Custom scrollbar for all pages */
::-webkit-scrollbar {
    width: 3px;
}
::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
}
::-webkit-scrollbar-thumb {
    background: #05c3b6;
    border-radius: 3px;
}
::-webkit-scrollbar-thumb:hover {
    background: #04a89d;
}

.container { 
    max-width: 1000px; 
    margin: auto; 
    background-color: #2b9869; 
    padding: 20px; 
    border-radius: 14px; 
    box-shadow: 0 5px 15px rgba(0,0,0,0.5); 
}

h1, h2 { 
    color: #00ffff; 
    text-align: center; 
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

h3, h4, h5, h6 { 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8); 
}

p, div, span { 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6); 
}

#welcome-msg { 
    text-align: center; 
    font-size: 24px; 
    margin-bottom: 20px; 
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8); 
}

/* Wallet Section Styles */
.wallet-section { 
    margin: 20px 0; 
}

.wallet-card { 
    background: rgba(19,73,240,0.4); 
    padding: 10px; 
    border-radius: 10px; 
    margin-bottom: 20px; 
}

.wallet-balance { 
    text-align: center; 
    margin-bottom: 20px; 
}

.balance-amount { 
    font-size: 36px; 
    font-weight: bold; 
    color: #ffd700; 
    margin: 10px 0; 
}

.wallet-stats { 
    display: flex; 
    justify-content: space-around; 
    margin: 20px 0; 
    flex-wrap: wrap; 
    gap: 15px; 
}

.stat-item { 
    text-align: center; 
    flex: 1; 
    min-width: 150px; 
}

.stat-item h4 { 
    color: #00ffff; 
    margin: 0 0 10px 0; 
    font-size: 14px; 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8); 
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    background: linear-gradient(145deg, rgba(255,215,0,0.1), rgba(255,215,0,0.05));
    border: 2px solid rgba(255,215,0,0.3);
    border-radius: 10px;
    padding: 15px 10px;
    margin: 5px 0;
    box-shadow:
        0 4px 8px rgba(0,0,0,0.3),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.2);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-value::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.stat-value:hover {
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(0,0,0,0.4),
        inset 0 1px 0 rgba(255,255,255,0.2),
        inset 0 -1px 0 rgba(0,0,0,0.3);
}

.stat-value:hover::before {
    left: 100%;
}

.wallet-actions { 
    display: flex; 
    justify-content: center; 
    gap: 15px; 
    margin-top: 20px; 
}

/* Button Styles */
button {
    cursor: pointer;
    padding: 10px 15px;
    border: none;
    border-radius: 10px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

.deposit-btn { 
    background-color: #4CAF50; 
    color: white; 
}

.transfer-btn { 
    background-color: #2196F3; 
    color: white; 
}

.history-btn { 
    background-color: #9C27B0; 
    color: white; 
}

.btn-info { 
    background-color: #00bcd4; 
    color: white; 
}

.btn-success { 
    background-color: #4CAF50; 
    color: white; 
}

.btn-primary { 
    background-color: #007bff; 
    color: white; 
}

/* Service Prices Styles */
.service-prices-container { 
    padding: 20px; 
}

.service-category { 
    margin-bottom: 30px; 
}

.service-category h3 { 
    color: #00ffff; 
    margin-bottom: 15px; 
}

.prices-list { 
    background: rgba(0,0,0,0.3); 
    padding: 15px; 
    border-radius: 8px; 
    margin-bottom: 15px; 
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.price-item:last-child { 
    border-bottom: none; 
}

.price-name { 
    flex: 1; 
    color: white; 
}

.price-value { 
    color: #00ff00; 
    font-weight: bold; 
}

.price-unit { 
    color: #ccc; 
    font-size: 0.9em; 
    margin-left: 5px; 
}

.service-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

/* Order Modal Styles */
.order-modal-content { 
    padding: 20px; 
    max-width: 600px; 
}

.services-list { 
    max-height: 400px; 
    overflow-y: auto; 
    margin: 20px 0; 
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 8px;
    margin-bottom: 10px;
    background: rgba(0,0,0,0.3);
}

.service-info { 
    flex: 1; 
}

.service-name { 
    color: white; 
    font-weight: bold; 
    margin-bottom: 5px; 
}

.service-price { 
    color: #00ff00; 
}

.quantity-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-control button {
    width: 30px;
    height: 30px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
}

.quantity-control input {
    width: 60px;
    text-align: center;
    padding: 5px;
    border: 1px solid #555;
    border-radius: 4px;
    background: #222;
    color: white;
}

.order-summary {
    background: rgba(0,0,0,0.5);
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
}

.summary-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    color: white;
}

.summary-line.total {
    font-weight: bold;
    font-size: 1.2em;
    border-top: 1px solid rgba(255,255,255,0.3);
    padding-top: 10px;
    color: #00ff00;
}

.order-options { 
    margin: 20px 0; 
}

.order-options label {
    display: block;
    color: white;
    margin-bottom: 10px;
}

.order-options textarea {
    width: 100%;
    height: 80px;
    padding: 10px;
    border: 1px solid #555;
    border-radius: 4px;
    background: #ffc000;
    color: black;
    resize: vertical;
}

.order-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

/* Amount Selection Buttons */
.amount-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.amount-btn {
    padding: 15px 10px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    position: relative;
    overflow: hidden;
}

.amount-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.amount-btn:hover {
    border-color: #00ffff;
    background: linear-gradient(145deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.1));
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.amount-btn:hover::before {
    left: 100%;
}

.amount-btn.selected {
    border-color: #00ff00;
    background: linear-gradient(145deg, rgba(0, 255, 0, 0.3), rgba(0, 255, 0, 0.1));
    color: #00ff00;
}

.amount-value {
    font-size: 20px;
    margin-bottom: 5px;
}

.amount-label {
    font-size: 12px;
    color: #ccc;
}

/* Payment Method Cards */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.payment-method {
    padding: 20px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.payment-method::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.payment-method:hover {
    border-color: #ffd700;
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.payment-method:hover::before {
    left: 100%;
}

.payment-method.selected {
    border-color: #00ff00;
    background: linear-gradient(145deg, rgba(0, 255, 0, 0.3), rgba(0, 255, 0, 0.1));
}

.payment-icon {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.payment-name {
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.payment-desc {
    color: #ccc;
    font-size: 11px;
    margin-top: 5px;
}
