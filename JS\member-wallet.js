// Member Wallet and Modal Functions

// Deposit functions
function openDepositModal() {
    showModal('deposit-modal', '💳 Deposit Credits', `
        <div class="modal-form">
            <div class="form-group">
                <label style="color: #00ffff; font-size: 18px; margin-bottom: 15px; display: block;">Select Amount</label>
                <div class="amount-grid">
                    <div class="amount-btn" onclick="selectDepositAmount(10, this)">
                        <div class="amount-value">$10</div>
                        <div class="amount-label">Starter</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(20, this)">
                        <div class="amount-value">$20</div>
                        <div class="amount-label">Basic</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(50, this)">
                        <div class="amount-value">$50</div>
                        <div class="amount-label">Popular</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(100, this)">
                        <div class="amount-value">$100</div>
                        <div class="amount-label">Standard</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(500, this)">
                        <div class="amount-value">$500</div>
                        <div class="amount-label">Premium</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(1000, this)">
                        <div class="amount-value">$1,000</div>
                        <div class="amount-label">Pro</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(2000, this)">
                        <div class="amount-value">$2,000</div>
                        <div class="amount-label">Business</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(3000, this)">
                        <div class="amount-value">$3,000</div>
                        <div class="amount-label">Elite</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(4000, this)">
                        <div class="amount-value">$4,000</div>
                        <div class="amount-label">VIP</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(5000, this)">
                        <div class="amount-value">$5,000</div>
                        <div class="amount-label">Ultimate</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(6000, this)">
                        <div class="amount-value">$6,000</div>
                        <div class="amount-label">Platinum</div>
                    </div>
                    <div class="amount-btn" onclick="selectDepositAmount(9500, this)">
                        <div class="amount-value">$9,500</div>
                        <div class="amount-label">Diamond</div>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <label style="color: #00ffff; margin-bottom: 10px; display: block;">Custom Amount</label>
                    <input type="number" id="custom-deposit-amount" min="10" max="10000" step="0.01"
                           placeholder="Enter custom amount"
                           style="width: 100%; padding: 12px; border-radius: 8px; background: #333; color: white; border: 2px solid #555;"
                           onchange="selectCustomAmount(this.value)">
                </div>
            </div>

            <div class="form-group">
                <label style="color: #00ffff; font-size: 18px; margin-bottom: 15px; display: block;">Payment Method</label>
                <div class="payment-methods">
                    <div class="payment-method" onclick="selectPaymentMethod('paypal', this)">
                        <span class="payment-icon">💙</span>
                        <div class="payment-name">PayPal</div>
                        <div class="payment-desc">Secure & Fast</div>
                    </div>
                    <div class="payment-method" onclick="selectPaymentMethod('stripe', this)">
                        <span class="payment-icon">💳</span>
                        <div class="payment-name">Credit Card</div>
                        <div class="payment-desc">Visa, MasterCard</div>
                    </div>
                    <div class="payment-method" onclick="selectPaymentMethod('square', this)">
                        <span class="payment-icon">⬜</span>
                        <div class="payment-name">Square</div>
                        <div class="payment-desc">Secure Payment</div>
                    </div>
                    <div class="payment-method" onclick="selectPaymentMethod('debit', this)">
                        <span class="payment-icon">💰</span>
                        <div class="payment-name">Debit Card</div>
                        <div class="payment-desc">Direct Payment</div>
                    </div>
                    <div class="payment-method" onclick="selectPaymentMethod('venmo', this)">
                        <span class="payment-icon">📱</span>
                        <div class="payment-name">Venmo</div>
                        <div class="payment-desc">Mobile Payment</div>
                    </div>
                    <div class="payment-method" onclick="selectPaymentMethod('zelle', this)">
                        <span class="payment-icon">⚡</span>
                        <div class="payment-name">Zelle</div>
                        <div class="payment-desc">Bank Transfer</div>
                    </div>
                </div>
            </div>

            <div id="deposit-summary" style="display: none; margin: 20px 0; padding: 15px; background: rgba(0,255,255,0.1); border-radius: 10px; border: 2px solid #00ffff;">
                <h4 style="color: #00ffff; margin: 0 0 10px 0;">Deposit Summary</h4>
                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Amount:</span>
                    <span id="summary-amount" style="color: #00ff00; font-weight: bold;">$0.00</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Payment Method:</span>
                    <span id="summary-method" style="color: #ffd700; font-weight: bold;">-</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Processing Fee:</span>
                    <span id="summary-fee" style="color: #ff9800;">$0.00</span>
                </div>
                <hr style="border: 1px solid rgba(255,255,255,0.2); margin: 10px 0;">
                <div style="display: flex; justify-content: space-between; margin: 5px 0; font-size: 18px;">
                    <span style="font-weight: bold;">Total:</span>
                    <span id="summary-total" style="color: #00ff00; font-weight: bold; font-size: 20px;">$0.00</span>
                </div>
            </div>
        </div>
    `, [
        { text: 'Cancel', class: 'secondary', onclick: 'closeModal()' },
        { text: 'Proceed to Payment', class: 'primary', onclick: 'submitDeposit()' }
    ]);
}

function selectDepositAmount(amount, button) {
    selectedDepositAmount = amount;

    // Update button states
    document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));
    button.classList.add('selected');

    // Clear custom input
    document.getElementById('custom-deposit-amount').value = '';

    updateDepositSummary();
}

function selectCustomAmount(amount) {
    if (amount && amount >= 10) {
        selectedDepositAmount = parseFloat(amount);

        // Clear button selections
        document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));

        updateDepositSummary();
    }
}

function selectPaymentMethod(method, button) {
    selectedPaymentMethod = method;

    // Update button states
    document.querySelectorAll('.payment-method').forEach(btn => btn.classList.remove('selected'));
    button.classList.add('selected');

    updateDepositSummary();
}

function updateDepositSummary() {
    const summaryDiv = document.getElementById('deposit-summary');

    if (selectedDepositAmount > 0 && selectedPaymentMethod) {
        // Calculate fees based on payment method
        let fee = 0;
        const feeRates = {
            'paypal': 0.029,
            'stripe': 0.029,
            'square': 0.026,
            'debit': 0.015,
            'venmo': 0.019,
            'zelle': 0.01
        };

        fee = selectedDepositAmount * (feeRates[selectedPaymentMethod] || 0.029);
        const total = selectedDepositAmount + fee;

        // Update summary display
        document.getElementById('summary-amount').textContent = '$' + selectedDepositAmount.toFixed(2);
        document.getElementById('summary-method').textContent = selectedPaymentMethod.charAt(0).toUpperCase() + selectedPaymentMethod.slice(1);
        document.getElementById('summary-fee').textContent = '$' + fee.toFixed(2);
        document.getElementById('summary-total').textContent = '$' + total.toFixed(2);

        summaryDiv.style.display = 'block';
    } else {
        summaryDiv.style.display = 'none';
    }
}

function submitDeposit() {
    if (!selectedDepositAmount || selectedDepositAmount <= 0) {
        showCustomAlert('error', 'Invalid Amount', 'Please select or enter a valid deposit amount.');
        return;
    }

    if (!selectedPaymentMethod) {
        showCustomAlert('error', 'Payment Method Required', 'Please select a payment method.');
        return;
    }

    // Calculate total with fees
    const feeRates = {
        'paypal': 0.029,
        'stripe': 0.029,
        'square': 0.026,
        'debit': 0.015,
        'venmo': 0.019,
        'zelle': 0.01
    };

    const fee = selectedDepositAmount * (feeRates[selectedPaymentMethod] || 0.029);
    const totalAmount = selectedDepositAmount + fee;

    processPayment(selectedDepositAmount, selectedPaymentMethod, totalAmount);
}

function processPayment(amount, method, totalAmount) {
    // Show processing message
    showCustomAlert('info', 'Processing Payment', 'Redirecting to payment processor...');

    // Create form data
    const formData = new FormData();
    formData.append('action', 'create_deposit');
    formData.append('amount', amount);
    formData.append('payment_method', method);
    formData.append('total_amount', totalAmount);

    fetch('credit_wallet.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.redirect_url) {
                // Redirect to payment processor
                window.location.href = data.redirect_url;
            } else {
                showCustomAlert('success', 'Deposit Initiated', 'Your deposit has been initiated successfully!');
                closeModal();
                loadWalletInfo(); // Refresh wallet balance
            }
        } else {
            showCustomAlert('error', 'Deposit Failed', 'Error processing deposit: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showCustomAlert('error', 'Connection Error', 'Error processing deposit');
    });
}

// Modal utility functions
function showModal(id, title, content, buttons = []) {
    // Remove existing modal if any
    const existingModal = document.getElementById('custom-modal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = 'custom-modal';
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>${title}</h2>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            ${buttons.length > 0 ? `
                <div class="modal-footer">
                    ${buttons.map(btn => `<button class="btn btn-${btn.class}" onclick="${btn.onclick}">${btn.text}</button>`).join('')}
                </div>
            ` : ''}
        </div>
    `;

    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

function closeModal() {
    const modal = document.getElementById('custom-modal');
    if (modal) {
        modal.remove();
    }
}
